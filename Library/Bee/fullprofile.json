{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 75155, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 75155, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 75155, "tid": 1786, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 75155, "tid": 1786, "ts": 1751873315004606, "dur": 1800, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 75155, "tid": 1786, "ts": 1751873315011960, "dur": 1278, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 75155, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314449859, "dur": 7118, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314456980, "dur": 535640, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314457125, "dur": 127, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314457358, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314457361, "dur": 167412, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314624777, "dur": 18, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314624796, "dur": 1418, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626225, "dur": 2, "ph": "X", "name": "ProcessMessages 2169", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626228, "dur": 39, "ph": "X", "name": "ReadAsync 2169", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626276, "dur": 1, "ph": "X", "name": "ProcessMessages 1225", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626287, "dur": 53, "ph": "X", "name": "ReadAsync 1225", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626342, "dur": 1, "ph": "X", "name": "ProcessMessages 1220", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626344, "dur": 34, "ph": "X", "name": "ReadAsync 1220", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626383, "dur": 27, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626412, "dur": 40, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626462, "dur": 6, "ph": "X", "name": "ProcessMessages 1221", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626469, "dur": 29, "ph": "X", "name": "ReadAsync 1221", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626502, "dur": 23, "ph": "X", "name": "ReadAsync 1204", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626527, "dur": 44, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626573, "dur": 36, "ph": "X", "name": "ReadAsync 1148", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626618, "dur": 30, "ph": "X", "name": "ReadAsync 1110", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626649, "dur": 1, "ph": "X", "name": "ProcessMessages 1056", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626653, "dur": 29, "ph": "X", "name": "ReadAsync 1056", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626684, "dur": 16, "ph": "X", "name": "ReadAsync 892", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626708, "dur": 18, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626728, "dur": 21, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626751, "dur": 51, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626804, "dur": 1, "ph": "X", "name": "ProcessMessages 1458", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626805, "dur": 29, "ph": "X", "name": "ReadAsync 1458", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626842, "dur": 18, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626863, "dur": 28, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626893, "dur": 24, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626919, "dur": 22, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626954, "dur": 27, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626982, "dur": 1, "ph": "X", "name": "ProcessMessages 1198", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314626984, "dur": 32, "ph": "X", "name": "ReadAsync 1198", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627017, "dur": 29, "ph": "X", "name": "ReadAsync 902", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627049, "dur": 17, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627073, "dur": 16, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627091, "dur": 32, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627125, "dur": 38, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627164, "dur": 16, "ph": "X", "name": "ReadAsync 904", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627182, "dur": 16, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627206, "dur": 30, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627239, "dur": 43, "ph": "X", "name": "ReadAsync 1004", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627282, "dur": 1, "ph": "X", "name": "ProcessMessages 1117", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627284, "dur": 24, "ph": "X", "name": "ReadAsync 1117", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627309, "dur": 29, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627341, "dur": 47, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627408, "dur": 1, "ph": "X", "name": "ProcessMessages 1301", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627409, "dur": 52, "ph": "X", "name": "ReadAsync 1301", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627465, "dur": 40, "ph": "X", "name": "ReadAsync 1217", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627506, "dur": 1, "ph": "X", "name": "ProcessMessages 1303", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627508, "dur": 25, "ph": "X", "name": "ReadAsync 1303", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627534, "dur": 38, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627574, "dur": 21, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627597, "dur": 15, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627620, "dur": 19, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627640, "dur": 19, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627660, "dur": 9, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627670, "dur": 33, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627705, "dur": 20, "ph": "X", "name": "ReadAsync 1097", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627727, "dur": 31, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627783, "dur": 32, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627817, "dur": 49, "ph": "X", "name": "ReadAsync 1150", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627868, "dur": 1, "ph": "X", "name": "ProcessMessages 1473", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314627869, "dur": 431, "ph": "X", "name": "ReadAsync 1473", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314628302, "dur": 3, "ph": "X", "name": "ProcessMessages 5660", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314628306, "dur": 96, "ph": "X", "name": "ReadAsync 5660", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314628420, "dur": 1, "ph": "X", "name": "ProcessMessages 1505", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314628421, "dur": 84, "ph": "X", "name": "ReadAsync 1505", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314628507, "dur": 2, "ph": "X", "name": "ProcessMessages 2555", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314628509, "dur": 28, "ph": "X", "name": "ReadAsync 2555", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314628540, "dur": 36, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314628577, "dur": 1, "ph": "X", "name": "ProcessMessages 862", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314628579, "dur": 31, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314628612, "dur": 21, "ph": "X", "name": "ReadAsync 835", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314628635, "dur": 21, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314628657, "dur": 92, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314628751, "dur": 1, "ph": "X", "name": "ProcessMessages 1853", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314628752, "dur": 17, "ph": "X", "name": "ReadAsync 1853", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314628772, "dur": 18, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314628794, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314628795, "dur": 52, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314628849, "dur": 20, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314628875, "dur": 1, "ph": "X", "name": "ProcessMessages 1125", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314628877, "dur": 28, "ph": "X", "name": "ReadAsync 1125", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314628906, "dur": 92, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314628999, "dur": 1, "ph": "X", "name": "ProcessMessages 1963", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629001, "dur": 46, "ph": "X", "name": "ReadAsync 1963", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629049, "dur": 17, "ph": "X", "name": "ReadAsync 1130", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629069, "dur": 17, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629087, "dur": 17, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629106, "dur": 21, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629129, "dur": 36, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629167, "dur": 4, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629172, "dur": 21, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629195, "dur": 17, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629214, "dur": 25, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629242, "dur": 26, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629270, "dur": 36, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629308, "dur": 19, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629329, "dur": 25, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629356, "dur": 26, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629384, "dur": 25, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629411, "dur": 15, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629428, "dur": 18, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629448, "dur": 31, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629481, "dur": 27, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629510, "dur": 17, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629529, "dur": 19, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629550, "dur": 16, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629568, "dur": 17, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629586, "dur": 20, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629608, "dur": 24, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629640, "dur": 69, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629710, "dur": 1, "ph": "X", "name": "ProcessMessages 1561", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629712, "dur": 24, "ph": "X", "name": "ReadAsync 1561", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629738, "dur": 37, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629776, "dur": 1, "ph": "X", "name": "ProcessMessages 1217", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629777, "dur": 38, "ph": "X", "name": "ReadAsync 1217", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629818, "dur": 41, "ph": "X", "name": "ReadAsync 1123", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629860, "dur": 18, "ph": "X", "name": "ReadAsync 1089", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629881, "dur": 34, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629922, "dur": 15, "ph": "X", "name": "ReadAsync 1009", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629939, "dur": 21, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629962, "dur": 15, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629979, "dur": 17, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314629997, "dur": 19, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630018, "dur": 23, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630043, "dur": 30, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630075, "dur": 16, "ph": "X", "name": "ReadAsync 955", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630092, "dur": 19, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630113, "dur": 30, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630145, "dur": 29, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630177, "dur": 42, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630221, "dur": 29, "ph": "X", "name": "ReadAsync 1159", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630252, "dur": 34, "ph": "X", "name": "ReadAsync 899", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630288, "dur": 17, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630308, "dur": 43, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630370, "dur": 21, "ph": "X", "name": "ReadAsync 962", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630393, "dur": 22, "ph": "X", "name": "ReadAsync 923", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630417, "dur": 31, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630450, "dur": 17, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630469, "dur": 18, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630489, "dur": 22, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630513, "dur": 39, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630554, "dur": 18, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630574, "dur": 34, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630610, "dur": 76, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630687, "dur": 1, "ph": "X", "name": "ProcessMessages 1775", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630689, "dur": 36, "ph": "X", "name": "ReadAsync 1775", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630728, "dur": 43, "ph": "X", "name": "ReadAsync 1126", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630773, "dur": 33, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630808, "dur": 17, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630827, "dur": 19, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630849, "dur": 14, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630865, "dur": 17, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630884, "dur": 14, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630900, "dur": 18, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630920, "dur": 18, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314630940, "dur": 87, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631029, "dur": 34, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631065, "dur": 34, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631105, "dur": 32, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631139, "dur": 31, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631172, "dur": 16, "ph": "X", "name": "ReadAsync 788", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631190, "dur": 34, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631226, "dur": 45, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631271, "dur": 1, "ph": "X", "name": "ProcessMessages 1224", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631273, "dur": 34, "ph": "X", "name": "ReadAsync 1224", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631309, "dur": 53, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631365, "dur": 43, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631409, "dur": 1, "ph": "X", "name": "ProcessMessages 1714", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631410, "dur": 31, "ph": "X", "name": "ReadAsync 1714", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631443, "dur": 49, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631494, "dur": 56, "ph": "X", "name": "ReadAsync 1291", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631551, "dur": 1, "ph": "X", "name": "ProcessMessages 1425", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631552, "dur": 64, "ph": "X", "name": "ReadAsync 1425", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631617, "dur": 1, "ph": "X", "name": "ProcessMessages 1475", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631619, "dur": 23, "ph": "X", "name": "ReadAsync 1475", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631644, "dur": 32, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631679, "dur": 18, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631699, "dur": 22, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631722, "dur": 61, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631785, "dur": 1, "ph": "X", "name": "ProcessMessages 1469", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631786, "dur": 45, "ph": "X", "name": "ReadAsync 1469", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631836, "dur": 45, "ph": "X", "name": "ReadAsync 1043", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631884, "dur": 89, "ph": "X", "name": "ReadAsync 979", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631974, "dur": 1, "ph": "X", "name": "ProcessMessages 2231", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314631976, "dur": 23, "ph": "X", "name": "ReadAsync 2231", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635084, "dur": 51, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635137, "dur": 5, "ph": "X", "name": "ProcessMessages 8191", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635142, "dur": 20, "ph": "X", "name": "ReadAsync 8191", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635179, "dur": 21, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635203, "dur": 19, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635224, "dur": 52, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635277, "dur": 5, "ph": "X", "name": "ProcessMessages 1138", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635282, "dur": 21, "ph": "X", "name": "ReadAsync 1138", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635306, "dur": 17, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635325, "dur": 36, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635362, "dur": 5, "ph": "X", "name": "ProcessMessages 879", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635368, "dur": 19, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635389, "dur": 19, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635411, "dur": 27, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635440, "dur": 23, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635472, "dur": 66, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635546, "dur": 5, "ph": "X", "name": "ProcessMessages 1679", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635553, "dur": 74, "ph": "X", "name": "ReadAsync 1679", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635629, "dur": 1, "ph": "X", "name": "ProcessMessages 2430", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635631, "dur": 80, "ph": "X", "name": "ReadAsync 2430", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635715, "dur": 3, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635719, "dur": 67, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635789, "dur": 1, "ph": "X", "name": "ProcessMessages 1747", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635791, "dur": 60, "ph": "X", "name": "ReadAsync 1747", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635853, "dur": 1, "ph": "X", "name": "ProcessMessages 1024", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635854, "dur": 20, "ph": "X", "name": "ReadAsync 1024", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635876, "dur": 28, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635907, "dur": 44, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635952, "dur": 1, "ph": "X", "name": "ProcessMessages 1305", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635954, "dur": 39, "ph": "X", "name": "ReadAsync 1305", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314635995, "dur": 45, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636057, "dur": 1, "ph": "X", "name": "ProcessMessages 951", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636059, "dur": 76, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636138, "dur": 35, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636176, "dur": 17, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636195, "dur": 24, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636227, "dur": 69, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636301, "dur": 3, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636305, "dur": 51, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636357, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636359, "dur": 35, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636396, "dur": 41, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636442, "dur": 21, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636465, "dur": 16, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636490, "dur": 58, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636550, "dur": 94, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636646, "dur": 50, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636699, "dur": 56, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636756, "dur": 30, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636788, "dur": 27, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636817, "dur": 36, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636855, "dur": 17, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636874, "dur": 16, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636892, "dur": 59, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314636958, "dur": 68, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637028, "dur": 32, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637063, "dur": 22, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637087, "dur": 39, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637128, "dur": 14, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637144, "dur": 40, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637186, "dur": 18, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637206, "dur": 47, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637255, "dur": 62, "ph": "X", "name": "ReadAsync 1011", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637319, "dur": 42, "ph": "X", "name": "ReadAsync 969", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637363, "dur": 20, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637385, "dur": 41, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637427, "dur": 36, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637465, "dur": 37, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637507, "dur": 49, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637558, "dur": 46, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637610, "dur": 21, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637634, "dur": 17, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637653, "dur": 35, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637690, "dur": 17, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637709, "dur": 15, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637725, "dur": 21, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637748, "dur": 18, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637768, "dur": 62, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637832, "dur": 61, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637895, "dur": 21, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637917, "dur": 18, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637937, "dur": 38, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637977, "dur": 15, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314637994, "dur": 27, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638024, "dur": 61, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638087, "dur": 52, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638141, "dur": 34, "ph": "X", "name": "ReadAsync 1185", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638178, "dur": 18, "ph": "X", "name": "ReadAsync 1040", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638198, "dur": 78, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638278, "dur": 31, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638315, "dur": 21, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638337, "dur": 33, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638372, "dur": 14, "ph": "X", "name": "ReadAsync 991", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638392, "dur": 4, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638397, "dur": 15, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638422, "dur": 68, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638492, "dur": 18, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638513, "dur": 22, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638537, "dur": 45, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638584, "dur": 21, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638607, "dur": 16, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638625, "dur": 53, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638680, "dur": 19, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638701, "dur": 33, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638736, "dur": 39, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638777, "dur": 19, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638798, "dur": 54, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638855, "dur": 15, "ph": "X", "name": "ReadAsync 986", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638872, "dur": 75, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638952, "dur": 19, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314638973, "dur": 38, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314639013, "dur": 37, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314639064, "dur": 18, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314639085, "dur": 29, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314639115, "dur": 40, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314639157, "dur": 56, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314639216, "dur": 49, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314639266, "dur": 17, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314639287, "dur": 68, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314639357, "dur": 58, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314639417, "dur": 25, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314639444, "dur": 73, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314639519, "dur": 57, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314639578, "dur": 68, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314639648, "dur": 52, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314639711, "dur": 35, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314639752, "dur": 41, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314639794, "dur": 49, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314639846, "dur": 31, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314639879, "dur": 56, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314639937, "dur": 52, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314639991, "dur": 20, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314640013, "dur": 68, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314640083, "dur": 68, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314640152, "dur": 4, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314640157, "dur": 54, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314640220, "dur": 63, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314640284, "dur": 1, "ph": "X", "name": "ProcessMessages 1098", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314640285, "dur": 96, "ph": "X", "name": "ReadAsync 1098", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314640382, "dur": 1, "ph": "X", "name": "ProcessMessages 1311", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314640384, "dur": 18, "ph": "X", "name": "ReadAsync 1311", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314640404, "dur": 19, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314640425, "dur": 54, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314640481, "dur": 18, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314640502, "dur": 22, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314640526, "dur": 20, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314640548, "dur": 22, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314640572, "dur": 49, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314640622, "dur": 112, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314640737, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314640770, "dur": 121, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314640893, "dur": 158, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314641053, "dur": 76, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314641132, "dur": 189, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314641329, "dur": 86, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314641440, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314641462, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314641532, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314641613, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314641701, "dur": 61, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314641765, "dur": 213, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314641980, "dur": 74, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314642056, "dur": 119, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314642178, "dur": 83, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314642273, "dur": 235, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314642514, "dur": 237, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314642754, "dur": 58, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314642815, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314642948, "dur": 88, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314643043, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314643047, "dur": 183, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314643231, "dur": 6, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314643238, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314643278, "dur": 113, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314643393, "dur": 143, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314643541, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314643545, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314643643, "dur": 208, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314643854, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314643857, "dur": 207, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314644067, "dur": 104, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314644172, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314644176, "dur": 115, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314644295, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314644397, "dur": 56, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314644456, "dur": 129, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314644587, "dur": 88, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314644679, "dur": 118, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314644799, "dur": 124, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314644925, "dur": 158, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314645088, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314645093, "dur": 58, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314645155, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314645298, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314645301, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314645359, "dur": 227, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314645589, "dur": 85, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314645676, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314645746, "dur": 129, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314645878, "dur": 134, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314646016, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314646020, "dur": 74, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314646096, "dur": 9, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314646107, "dur": 54, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314646165, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314646250, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314646252, "dur": 62, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314646317, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314646321, "dur": 77, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314646401, "dur": 55, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314646459, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314646462, "dur": 73, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314646536, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314646538, "dur": 58, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314646600, "dur": 70, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314646673, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314646676, "dur": 67, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314646745, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314646749, "dur": 78, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314646830, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314646833, "dur": 138, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314646974, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314646977, "dur": 49, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314647029, "dur": 165, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314647196, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314647222, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314647266, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314647272, "dur": 196, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314647469, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314647471, "dur": 110, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314647584, "dur": 31, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314647617, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314647692, "dur": 8, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314647702, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314647776, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314647789, "dur": 58, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314647850, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314647853, "dur": 69, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314647925, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314647928, "dur": 60, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314647990, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314647993, "dur": 72, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314648067, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314648070, "dur": 90, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314648164, "dur": 8, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314648174, "dur": 55, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314648231, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314648233, "dur": 245, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314648481, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314648485, "dur": 112, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314648600, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314648603, "dur": 87, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314648693, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314648697, "dur": 60, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314648760, "dur": 58, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314648834, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314648837, "dur": 57, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314648897, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314648900, "dur": 163, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314649066, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314649069, "dur": 47, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314649118, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314649157, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314649160, "dur": 149, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314649312, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314649314, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314649360, "dur": 75, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314649437, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314649440, "dur": 60, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314649503, "dur": 151, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314649656, "dur": 146, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314649806, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314649810, "dur": 145, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314649963, "dur": 96, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314650060, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314650104, "dur": 179, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314650286, "dur": 45, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314650340, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314650387, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314650494, "dur": 63, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314650563, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314650567, "dur": 83, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314650653, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314650688, "dur": 57, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314650752, "dur": 60, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314650816, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314650819, "dur": 235, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314651057, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314651060, "dur": 88, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314651151, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314651155, "dur": 52, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314651210, "dur": 100, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314651312, "dur": 218, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314651532, "dur": 268, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314651802, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314651804, "dur": 1170, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314652981, "dur": 79, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314653077, "dur": 14072, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314667154, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314667156, "dur": 94, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314667253, "dur": 1919, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314669176, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314669269, "dur": 402, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314669673, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314669737, "dur": 1565, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314671304, "dur": 9168, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314680477, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314680491, "dur": 186, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314680680, "dur": 325, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314681015, "dur": 129, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314681148, "dur": 165, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314681314, "dur": 119, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314681436, "dur": 155, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314681593, "dur": 386, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314681982, "dur": 178, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314682162, "dur": 119, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314682283, "dur": 100, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314682385, "dur": 149, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314682541, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314682545, "dur": 120, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314682673, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314682804, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314682887, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314682891, "dur": 474, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314683369, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314683371, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314683551, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314683554, "dur": 116, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314683684, "dur": 478, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314684165, "dur": 298, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314684465, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314684563, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314684566, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314684691, "dur": 223, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314684917, "dur": 371, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314685291, "dur": 155, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314685449, "dur": 142, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314685593, "dur": 115, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314685710, "dur": 142, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314685854, "dur": 26, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314685881, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314685980, "dur": 442, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314686425, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314686428, "dur": 235, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314686666, "dur": 490, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314687158, "dur": 101, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314687261, "dur": 171, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314687435, "dur": 503, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314687950, "dur": 88, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314688039, "dur": 354, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314688396, "dur": 148, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314688546, "dur": 142, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314688691, "dur": 190, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314688883, "dur": 274, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314689159, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314689252, "dur": 376, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314689635, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314689640, "dur": 171, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314689814, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314689816, "dur": 320, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314690139, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314690141, "dur": 315, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314690459, "dur": 144, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314690605, "dur": 408, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314691015, "dur": 142, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314691162, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314691166, "dur": 273, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314691441, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314691544, "dur": 413, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314691979, "dur": 316, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314692297, "dur": 481, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314692781, "dur": 157, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314692939, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314692941, "dur": 230, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314693173, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314693293, "dur": 108, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314693403, "dur": 112, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314693518, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314693609, "dur": 114, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314693726, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314693760, "dur": 185, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314693948, "dur": 194, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314694144, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314694239, "dur": 100, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314694341, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314694411, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314694415, "dur": 427, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314694844, "dur": 124, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314694970, "dur": 380, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314695352, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314695418, "dur": 162, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314695582, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314695584, "dur": 297, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314695889, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314695894, "dur": 157, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314696053, "dur": 218, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314696282, "dur": 213, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314696500, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314696503, "dur": 140, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314696645, "dur": 580, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314697228, "dur": 107, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314697339, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314697343, "dur": 220, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314697564, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314697570, "dur": 298, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314697870, "dur": 136, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314698008, "dur": 125, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314698137, "dur": 141, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314698280, "dur": 157, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314698457, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314698461, "dur": 117, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314698579, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314698584, "dur": 222, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314698810, "dur": 189, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314699001, "dur": 469, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314699471, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314699472, "dur": 120969, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314820449, "dur": 37, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314820487, "dur": 2980, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314823472, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314823474, "dur": 81, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314823558, "dur": 125, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314823685, "dur": 89, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314824637, "dur": 51, "ph": "X", "name": "ReadAsync 7479", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314824692, "dur": 23, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314824720, "dur": 24, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314824745, "dur": 12, "ph": "X", "name": "ProcessMessages 2450", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314824758, "dur": 4353, "ph": "X", "name": "ReadAsync 2450", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314829114, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314829125, "dur": 502, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314829629, "dur": 1286, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314830935, "dur": 184, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314831125, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314831130, "dur": 253, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314831385, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314831387, "dur": 185, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314831575, "dur": 1428, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314833007, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314833010, "dur": 866, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314833886, "dur": 114, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314834002, "dur": 1642, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314835648, "dur": 431, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314836082, "dur": 702, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314836788, "dur": 201, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314836992, "dur": 2809, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314839804, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314839808, "dur": 630, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314840441, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314840514, "dur": 1093, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314841610, "dur": 182, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314841794, "dur": 1757, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314843555, "dur": 882, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314844441, "dur": 818, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314845266, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314845271, "dur": 375, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314845650, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314845653, "dur": 1877, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314847533, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314847535, "dur": 1438, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314848977, "dur": 1505, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314850485, "dur": 617, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314851105, "dur": 494, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314851602, "dur": 250, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314851854, "dur": 244, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314852100, "dur": 394, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314852503, "dur": 2086, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314854592, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314854594, "dur": 297, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314854898, "dur": 8, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314854908, "dur": 1043, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314855955, "dur": 123, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314856080, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314856187, "dur": 176, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314856366, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314856467, "dur": 94, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314856564, "dur": 167, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314856733, "dur": 133, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314856868, "dur": 116, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314856987, "dur": 89, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314857077, "dur": 244, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314857329, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314857334, "dur": 88, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314857425, "dur": 154, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314857581, "dur": 155, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314857739, "dur": 207, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314857949, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314857989, "dur": 114, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314858105, "dur": 85, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314858192, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314858221, "dur": 6, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314858228, "dur": 136, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314858367, "dur": 97, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314858466, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314858505, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314858606, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314858610, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314858705, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314858763, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314858767, "dur": 94, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314858863, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314858866, "dur": 85, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314858953, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314858957, "dur": 334, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314859293, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314859296, "dur": 153, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314859453, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314859509, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314859581, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314859709, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314859713, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314859762, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314859786, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314859892, "dur": 158, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314860053, "dur": 96, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314860152, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314860193, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314860255, "dur": 259, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314860517, "dur": 103, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314860624, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314860640, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314860678, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314860752, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314860785, "dur": 29, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314860816, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314860869, "dur": 191, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314861062, "dur": 146, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314861210, "dur": 494, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314861712, "dur": 255, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314861969, "dur": 238, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314862209, "dur": 177, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314862394, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314862397, "dur": 183, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314862583, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314862659, "dur": 231, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314862893, "dur": 121520, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314984424, "dur": 3, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314984429, "dur": 67, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314984501, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314984503, "dur": 60, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314984568, "dur": 42, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314984612, "dur": 62, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314984675, "dur": 35, "ph": "X", "name": "ProcessMessages 4164", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314984712, "dur": 3640, "ph": "X", "name": "ReadAsync 4164", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314988358, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314988362, "dur": 605, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314988969, "dur": 40, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314989010, "dur": 268, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314989280, "dur": 1, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1751873314989282, "dur": 3318, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 75155, "tid": 1786, "ts": 1751873315013247, "dur": 1755, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 75155, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 75155, "tid": 17179869184, "ts": 1751873314446640, "dur": 49, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 75155, "tid": 17179869184, "ts": 1751873314446690, "dur": 10492, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 75155, "tid": 17179869184, "ts": 1751873314457183, "dur": 122, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 75155, "tid": 1786, "ts": 1751873315015012, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 75155, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 75155, "tid": 1, "ts": 1751873313064579, "dur": 5162, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 75155, "tid": 1, "ts": 1751873313069746, "dur": 32154, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 75155, "tid": 1, "ts": 1751873313101907, "dur": 103876, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 75155, "tid": 1786, "ts": 1751873315015020, "dur": 6, "ph": "X", "name": "", "args": {}}, {"pid": 75155, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313063169, "dur": 8514, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313071686, "dur": 357885, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313072455, "dur": 3279, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313075741, "dur": 1053, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313076801, "dur": 9237, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313086044, "dur": 300, "ph": "X", "name": "ProcessMessages 8178", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313086346, "dur": 361, "ph": "X", "name": "ReadAsync 8178", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313086709, "dur": 3, "ph": "X", "name": "ProcessMessages 7252", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313086714, "dur": 48, "ph": "X", "name": "ReadAsync 7252", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313086764, "dur": 3, "ph": "X", "name": "ProcessMessages 8116", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313086768, "dur": 39, "ph": "X", "name": "ReadAsync 8116", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313086853, "dur": 30, "ph": "X", "name": "ReadAsync 1168", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313086923, "dur": 1, "ph": "X", "name": "ProcessMessages 1851", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313086995, "dur": 44, "ph": "X", "name": "ReadAsync 1851", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313087043, "dur": 3, "ph": "X", "name": "ProcessMessages 4011", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313087046, "dur": 49, "ph": "X", "name": "ReadAsync 4011", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313087100, "dur": 1, "ph": "X", "name": "ProcessMessages 1872", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313087101, "dur": 77, "ph": "X", "name": "ReadAsync 1872", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313087180, "dur": 1, "ph": "X", "name": "ProcessMessages 2544", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313087182, "dur": 60, "ph": "X", "name": "ReadAsync 2544", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313087245, "dur": 47, "ph": "X", "name": "ReadAsync 917", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313087293, "dur": 22, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313087315, "dur": 145, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313087463, "dur": 9, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313087473, "dur": 521, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313087996, "dur": 1, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313088001, "dur": 46, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313088048, "dur": 29, "ph": "X", "name": "ProcessMessages 6097", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313088078, "dur": 23, "ph": "X", "name": "ReadAsync 6097", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313088144, "dur": 1, "ph": "X", "name": "ProcessMessages 979", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313088185, "dur": 37, "ph": "X", "name": "ReadAsync 979", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313088226, "dur": 1, "ph": "X", "name": "ProcessMessages 1840", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313088228, "dur": 250, "ph": "X", "name": "ReadAsync 1840", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313088505, "dur": 41, "ph": "X", "name": "ProcessMessages 2060", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313088549, "dur": 160, "ph": "X", "name": "ReadAsync 2060", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313088939, "dur": 1, "ph": "X", "name": "ProcessMessages 2137", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313088940, "dur": 19, "ph": "X", "name": "ReadAsync 2137", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313088961, "dur": 2, "ph": "X", "name": "ProcessMessages 3783", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313089122, "dur": 29, "ph": "X", "name": "ReadAsync 3783", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313089152, "dur": 1, "ph": "X", "name": "ProcessMessages 2843", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313089155, "dur": 19, "ph": "X", "name": "ReadAsync 2843", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313089176, "dur": 86, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313089306, "dur": 1, "ph": "X", "name": "ProcessMessages 1603", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313089308, "dur": 36, "ph": "X", "name": "ReadAsync 1603", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313089345, "dur": 1, "ph": "X", "name": "ProcessMessages 2029", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313089477, "dur": 38, "ph": "X", "name": "ReadAsync 2029", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313089517, "dur": 57, "ph": "X", "name": "ProcessMessages 3267", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313089575, "dur": 26, "ph": "X", "name": "ReadAsync 3267", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313089606, "dur": 1, "ph": "X", "name": "ProcessMessages 1960", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313089608, "dur": 23, "ph": "X", "name": "ReadAsync 1960", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313089633, "dur": 39, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313089677, "dur": 70, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313089748, "dur": 1, "ph": "X", "name": "ProcessMessages 1451", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313089753, "dur": 174, "ph": "X", "name": "ReadAsync 1451", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313089930, "dur": 35, "ph": "X", "name": "ReadAsync 1251", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313089967, "dur": 18, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313089986, "dur": 29, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313090017, "dur": 19, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313090038, "dur": 22, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313090062, "dur": 35, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313090098, "dur": 1, "ph": "X", "name": "ProcessMessages 908", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313090099, "dur": 30, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313090132, "dur": 33, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313090167, "dur": 19, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313090188, "dur": 28, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313090218, "dur": 33, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313090257, "dur": 35, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313090294, "dur": 31, "ph": "X", "name": "ReadAsync 1265", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313090327, "dur": 49, "ph": "X", "name": "ReadAsync 1031", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313090378, "dur": 36, "ph": "X", "name": "ReadAsync 972", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313090415, "dur": 1, "ph": "X", "name": "ProcessMessages 1060", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313090417, "dur": 114, "ph": "X", "name": "ReadAsync 1060", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313090532, "dur": 1, "ph": "X", "name": "ProcessMessages 2728", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313090613, "dur": 31, "ph": "X", "name": "ReadAsync 2728", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313090733, "dur": 1, "ph": "X", "name": "ProcessMessages 3529", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313090735, "dur": 30, "ph": "X", "name": "ReadAsync 3529", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313090767, "dur": 1, "ph": "X", "name": "ProcessMessages 3234", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313090769, "dur": 850, "ph": "X", "name": "ReadAsync 3234", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313091622, "dur": 4, "ph": "X", "name": "ProcessMessages 8148", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313091627, "dur": 31, "ph": "X", "name": "ReadAsync 8148", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313091660, "dur": 123, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313091786, "dur": 2, "ph": "X", "name": "ProcessMessages 2214", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313091789, "dur": 21, "ph": "X", "name": "ReadAsync 2214", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313091811, "dur": 1, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313091813, "dur": 40, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313091896, "dur": 23, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313091920, "dur": 1, "ph": "X", "name": "ProcessMessages 1766", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313091922, "dur": 43, "ph": "X", "name": "ReadAsync 1766", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313091968, "dur": 85, "ph": "X", "name": "ReadAsync 973", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092103, "dur": 30, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092134, "dur": 1, "ph": "X", "name": "ProcessMessages 1721", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092136, "dur": 31, "ph": "X", "name": "ReadAsync 1721", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092195, "dur": 44, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092240, "dur": 1, "ph": "X", "name": "ProcessMessages 1730", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092242, "dur": 20, "ph": "X", "name": "ReadAsync 1730", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092328, "dur": 50, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092380, "dur": 1, "ph": "X", "name": "ProcessMessages 2289", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092447, "dur": 22, "ph": "X", "name": "ReadAsync 2289", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092498, "dur": 1, "ph": "X", "name": "ProcessMessages 2334", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092500, "dur": 51, "ph": "X", "name": "ReadAsync 2334", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092552, "dur": 1, "ph": "X", "name": "ProcessMessages 1800", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092554, "dur": 23, "ph": "X", "name": "ReadAsync 1800", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092579, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092602, "dur": 21, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092625, "dur": 38, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092725, "dur": 1, "ph": "X", "name": "ProcessMessages 1017", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092728, "dur": 34, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092764, "dur": 1, "ph": "X", "name": "ProcessMessages 1992", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092766, "dur": 21, "ph": "X", "name": "ReadAsync 1992", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092791, "dur": 28, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092823, "dur": 22, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092847, "dur": 16, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092865, "dur": 26, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092894, "dur": 23, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313092921, "dur": 133, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313093057, "dur": 17, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313093081, "dur": 1, "ph": "X", "name": "ProcessMessages 2206", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313093656, "dur": 25, "ph": "X", "name": "ReadAsync 2206", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313093683, "dur": 4, "ph": "X", "name": "ProcessMessages 8191", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313093689, "dur": 17, "ph": "X", "name": "ReadAsync 8191", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313093708, "dur": 45, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313093759, "dur": 29, "ph": "X", "name": "ReadAsync 998", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313093789, "dur": 1, "ph": "X", "name": "ProcessMessages 922", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313093794, "dur": 17, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313093813, "dur": 26, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313093841, "dur": 23, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313093866, "dur": 1, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313093867, "dur": 32, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313093902, "dur": 25, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313093931, "dur": 34, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313093968, "dur": 22, "ph": "X", "name": "ReadAsync 1292", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313093999, "dur": 35, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094035, "dur": 1, "ph": "X", "name": "ProcessMessages 1143", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094037, "dur": 24, "ph": "X", "name": "ReadAsync 1143", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094065, "dur": 26, "ph": "X", "name": "ReadAsync 891", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094093, "dur": 18, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094114, "dur": 24, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094140, "dur": 19, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094162, "dur": 26, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094189, "dur": 15, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094209, "dur": 35, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094245, "dur": 1, "ph": "X", "name": "ProcessMessages 1151", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094247, "dur": 23, "ph": "X", "name": "ReadAsync 1151", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094272, "dur": 15, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094289, "dur": 18, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094312, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094314, "dur": 25, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094362, "dur": 22, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094387, "dur": 16, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094405, "dur": 28, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094436, "dur": 36, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094516, "dur": 14, "ph": "X", "name": "ReadAsync 1109", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094531, "dur": 1, "ph": "X", "name": "ProcessMessages 1390", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094533, "dur": 36, "ph": "X", "name": "ReadAsync 1390", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094571, "dur": 15, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094587, "dur": 17, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094607, "dur": 207, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094816, "dur": 94, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313094912, "dur": 495, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313095409, "dur": 81, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313095492, "dur": 545, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313096039, "dur": 25, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313096069, "dur": 469, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313096541, "dur": 568, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313097111, "dur": 16, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313097151, "dur": 127, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313097280, "dur": 494, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313097777, "dur": 468, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313098251, "dur": 155, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313098408, "dur": 452, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313098862, "dur": 15, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313098880, "dur": 634, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313099516, "dur": 137, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313099655, "dur": 612, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313100270, "dur": 480, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313100752, "dur": 16, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313100770, "dur": 600, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313101373, "dur": 91, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313101466, "dur": 594, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313102062, "dur": 519, "ph": "X", "name": "ReadAsync 1011", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313102584, "dur": 21, "ph": "X", "name": "ReadAsync 882", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313102607, "dur": 693, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313103302, "dur": 17, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313103321, "dur": 583, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313103906, "dur": 24, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313103936, "dur": 487, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313104425, "dur": 137, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313104564, "dur": 647, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313105214, "dur": 27, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313105243, "dur": 762, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313106008, "dur": 225, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313106235, "dur": 1356, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313107592, "dur": 1, "ph": "X", "name": "ProcessMessages 2298", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313107594, "dur": 707, "ph": "X", "name": "ReadAsync 2298", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313108406, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313108407, "dur": 20, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313108430, "dur": 792, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313109229, "dur": 4, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313109235, "dur": 552, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313110514, "dur": 1, "ph": "X", "name": "ProcessMessages 1231", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313110517, "dur": 160, "ph": "X", "name": "ReadAsync 1231", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313110682, "dur": 335, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313111018, "dur": 375, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313111396, "dur": 179, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313111576, "dur": 2, "ph": "X", "name": "ProcessMessages 2706", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313111579, "dur": 634, "ph": "X", "name": "ReadAsync 2706", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313112215, "dur": 174, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313112507, "dur": 983, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313113496, "dur": 6, "ph": "X", "name": "ProcessMessages 2700", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313113547, "dur": 1455, "ph": "X", "name": "ReadAsync 2700", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313115005, "dur": 1, "ph": "X", "name": "ProcessMessages 1692", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313115007, "dur": 326, "ph": "X", "name": "ReadAsync 1692", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313115334, "dur": 1, "ph": "X", "name": "ProcessMessages 2404", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313115375, "dur": 25, "ph": "X", "name": "ReadAsync 2404", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313115404, "dur": 999, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313116405, "dur": 378, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313116784, "dur": 37, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313116824, "dur": 30, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313116855, "dur": 135, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313116992, "dur": 1, "ph": "X", "name": "ProcessMessages 1216", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313116994, "dur": 26, "ph": "X", "name": "ReadAsync 1216", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313117021, "dur": 269, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313117293, "dur": 437, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313117735, "dur": 16, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313117753, "dur": 242, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313117997, "dur": 1, "ph": "X", "name": "ProcessMessages 1244", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313117999, "dur": 334, "ph": "X", "name": "ReadAsync 1244", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313118336, "dur": 693, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313119030, "dur": 1, "ph": "X", "name": "ProcessMessages 1531", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313119032, "dur": 158, "ph": "X", "name": "ReadAsync 1531", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313119192, "dur": 188, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313119383, "dur": 248, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313119640, "dur": 521, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313120162, "dur": 1, "ph": "X", "name": "ProcessMessages 1785", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313120164, "dur": 228, "ph": "X", "name": "ReadAsync 1785", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313120395, "dur": 305, "ph": "X", "name": "ReadAsync 1214", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313120702, "dur": 1, "ph": "X", "name": "ProcessMessages 843", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313120703, "dur": 386, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313121092, "dur": 33, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313121127, "dur": 503, "ph": "X", "name": "ReadAsync 1082", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313121636, "dur": 4, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313121642, "dur": 179, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313121823, "dur": 1, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313121825, "dur": 402, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313122230, "dur": 390, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313122622, "dur": 37, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313122661, "dur": 366, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313123030, "dur": 509, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313123541, "dur": 3, "ph": "X", "name": "ProcessMessages 2762", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313123544, "dur": 19733, "ph": "X", "name": "ReadAsync 2762", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313143286, "dur": 757, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313144048, "dur": 113, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313144164, "dur": 11, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313144176, "dur": 117, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313144296, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313144298, "dur": 327, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313144634, "dur": 384, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313145021, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313145026, "dur": 99, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313145128, "dur": 9, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313145139, "dur": 58, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313145200, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313145203, "dur": 194, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313145420, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313145421, "dur": 57, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313145482, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313145485, "dur": 282, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313145768, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313145771, "dur": 202, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313145976, "dur": 172, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313146158, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313146172, "dur": 105, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313146279, "dur": 322, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313146604, "dur": 43, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313146649, "dur": 457, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313147108, "dur": 43628, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313190742, "dur": 412, "ph": "X", "name": "ProcessMessages 2840", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313191156, "dur": 94997, "ph": "X", "name": "ReadAsync 2840", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313286166, "dur": 4, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313286177, "dur": 82, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313286265, "dur": 118, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313286386, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313286388, "dur": 64, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313286455, "dur": 1, "ph": "X", "name": "ProcessMessages 7896", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313286872, "dur": 129, "ph": "X", "name": "ReadAsync 7896", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313287022, "dur": 33, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313287057, "dur": 240, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313287302, "dur": 2871, "ph": "X", "name": "ProcessMessages 2033", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313290176, "dur": 135632, "ph": "X", "name": "ReadAsync 2033", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313425817, "dur": 27, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313425846, "dur": 644, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313426494, "dur": 230, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751873313426725, "dur": 2383, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 75155, "tid": 1786, "ts": 1751873315015027, "dur": 405, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 75155, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 75155, "tid": 8589934592, "ts": 1751873313060488, "dur": 145312, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 75155, "tid": 8589934592, "ts": 1751873313205803, "dur": 9, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 75155, "tid": 8589934592, "ts": 1751873313205813, "dur": 1805, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 75155, "tid": 1786, "ts": 1751873315015434, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 75155, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 75155, "tid": 4294967296, "ts": 1751873313017542, "dur": 412796, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 75155, "tid": 4294967296, "ts": 1751873313022438, "dur": 33331, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 75155, "tid": 4294967296, "ts": 1751873313430531, "dur": 995687, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 75155, "tid": 4294967296, "ts": 1751873314426626, "dur": 566385, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 75155, "tid": 4294967296, "ts": 1751873314427732, "dur": 18544, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 75155, "tid": 4294967296, "ts": 1751873314993025, "dur": 8683, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 75155, "tid": 4294967296, "ts": 1751873314997134, "dur": 3039, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 75155, "tid": 4294967296, "ts": 1751873315001712, "dur": 30, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 75155, "tid": 1786, "ts": 1751873315015440, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751873314456963, "dur": 169049, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751873314626016, "dur": 169, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751873314626251, "dur": 72, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751873314632538, "dur": 2816, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751873314635816, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751873314636280, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751873314626328, "dur": 14485, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751873314640822, "dur": 348334, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751873314989383, "dur": 532, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751873314626277, "dur": 14552, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314641097, "dur": 156, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1751873314641253, "dur": 1120, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 1, "ts": 1751873314642373, "dur": 653, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 1, "ts": 1751873314640831, "dur": 2195, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314643027, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_9179AE1968B95645.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873314643214, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314643390, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_462079EBF000A463.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873314643686, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314643973, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_65DFED6065744ECD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873314644180, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314644284, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_8D21704F39F1057D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873314644434, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314644577, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_A8529918C73873A7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873314644781, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_EB44D0B764B7C8DC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873314644944, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314645074, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873314645308, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314645529, "dur": 4783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873314650313, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314650423, "dur": 16683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751873314667107, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314667273, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873314667389, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314668324, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314669160, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314669883, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314670561, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314671179, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314671827, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314672445, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314673115, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314673893, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314674563, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314675154, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314675805, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314676499, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314677322, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314678136, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314678884, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314679268, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314679959, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314680187, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314680238, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314680363, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314680576, "dur": 638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314681214, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873314681305, "dur": 2351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751873314683657, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314683986, "dur": 938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873314684924, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314685014, "dur": 1624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751873314686639, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314686804, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873314687353, "dur": 850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751873314688203, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314688299, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873314688384, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751873314688812, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314688990, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873314689049, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314689105, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751873314689625, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314689885, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314689973, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873314690103, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314690171, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751873314690751, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314690872, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751873314691251, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314691348, "dur": 1737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751873314693086, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314693406, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Editor.ref.dll_B243E984A9C458BA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873314693469, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314693565, "dur": 689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873314694303, "dur": 1238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751873314695593, "dur": 966, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314696559, "dur": 2062, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314698623, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873314698757, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751873314699040, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314699165, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751873314699891, "dur": 120630, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751873314821237, "dur": 4740, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314825983, "dur": 2472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751873314828505, "dur": 2591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751873314831097, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314831172, "dur": 2839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751873314834012, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314834110, "dur": 4185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751873314838296, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314838458, "dur": 2373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751873314840831, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314840943, "dur": 4752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751873314845697, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314845818, "dur": 5344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751873314851162, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314851291, "dur": 9585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751873314860876, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314860991, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751873314861062, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314861222, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314861372, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314861442, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314861504, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314862066, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Path.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751873314862167, "dur": 638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873314862805, "dur": 126292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314626285, "dur": 14556, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314641014, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314641132, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_8B7F52F4EF803EEA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314641297, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314641408, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_E5822FB88B5F8525.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314641571, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314641655, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_54E2C669D74DB25A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314641794, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314641894, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_39B25D32CFFE745B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314642074, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314642153, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C9234425C371F41A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314642293, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314642424, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_58154A82C8AEEED2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314642607, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_6C9DA28BBAE3FB82.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314642812, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_0DC0DA8529CF1082.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314642912, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314642982, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_34F8FBC3386DE99E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314643173, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314643351, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_36001A2C42EABFDD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314643654, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314643761, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_ED9F64302AB7CA36.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314644110, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314644215, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_C1F8E474D4FC94AE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314644338, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314644431, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6799CBAE2582DA9C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314644578, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314644697, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_4782BF78D66FB2E5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314644824, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314644938, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_82FEB68469624F24.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314645106, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314645299, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_8C284CAFF6588524.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314645480, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314645642, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_A9C2D5B3A007F854.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314645787, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314645877, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_8895869DB6F5BBF5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314646048, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314646159, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314646249, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314646366, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314646518, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314646634, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314646762, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314646900, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314646995, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314647113, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314647201, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_7E6A236E3ABEDC63.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314647336, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314647536, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314647597, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314647792, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314647981, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314648113, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314648236, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314648336, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314648579, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314648727, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314648816, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314648942, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314649074, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314649354, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314649562, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314649648, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314649777, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314649938, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314650058, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314650239, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314650379, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314650524, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314650659, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314650802, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314650943, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314651247, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314651318, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314651437, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314651574, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314651667, "dur": 1896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314653564, "dur": 1711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314655276, "dur": 1645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314656921, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314658027, "dur": 1404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314659431, "dur": 1451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314660883, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314661812, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314662850, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314663728, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314664374, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314665348, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314666567, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314667501, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314668415, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314669227, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314669980, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314670647, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314671261, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314671881, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314672509, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314673190, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314673962, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314674629, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314675224, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314675888, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314676577, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314677422, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314678204, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314678966, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314679237, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314679946, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314680219, "dur": 91, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314680371, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314680602, "dur": 680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314681301, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314681602, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314681660, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751873314682246, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314682390, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314682446, "dur": 987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751873314683434, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314683625, "dur": 1462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751873314685088, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314685234, "dur": 639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314685873, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314685944, "dur": 1260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751873314687204, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314687398, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314687769, "dur": 2338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314690142, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314690633, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314690748, "dur": 1050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751873314691799, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314691889, "dur": 998, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751873314692888, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314693173, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_8EDD8A7C38E993CC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314693402, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.Editor.ref.dll_94629FB7B2A83F68.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314693545, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314693681, "dur": 823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314694541, "dur": 820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751873314695361, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314695443, "dur": 1114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314696557, "dur": 957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314697515, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873314697794, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751873314698222, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314698292, "dur": 127660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314825956, "dur": 4046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751873314830003, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314830079, "dur": 2658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751873314832788, "dur": 3433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751873314836221, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314836348, "dur": 5398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751873314841747, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314841857, "dur": 2724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751873314844581, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314844742, "dur": 3061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751873314847804, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314847908, "dur": 3862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751873314851770, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314851901, "dur": 2845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751873314854747, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314854823, "dur": 7046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751873314861869, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314862085, "dur": 653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314862780, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873314862857, "dur": 126270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314626291, "dur": 14562, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314641015, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_D7331C31AE624D95.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314641207, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314641337, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_FBD6848B40F9D84E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314641477, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314641581, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_479F21D64DDF5A5C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314641752, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314641858, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_91118A2F728DAD9D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314642034, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314642112, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_161CFFFA16B8213C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314642284, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314642411, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_061233166775A3D8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314642558, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314642645, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_845B1BE9B09512AE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314642777, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314642892, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_070DA0E44072E4AB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314643080, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0462768E64EABD0A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314643312, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314643494, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_FF3C62C209E1F2AF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314643728, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314643989, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_C59F701EDC7B3906.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314644175, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314644270, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_BA115901BA18BB2D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314644427, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314644552, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_06C0A05E69B9BD23.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314644720, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314644822, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_0E99BC6E1964CACE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314644981, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314645119, "dur": 4038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314649158, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314649347, "dur": 19871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751873314669219, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314669336, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314669445, "dur": 2001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314671481, "dur": 9051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751873314680598, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314680816, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751873314681213, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314681396, "dur": 1078, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751873314682474, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314682613, "dur": 985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314683598, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314683751, "dur": 1714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751873314685466, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314685599, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314685806, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314685954, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314686143, "dur": 1036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314687180, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314687267, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314687781, "dur": 3793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751873314691576, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314691738, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314691945, "dur": 1378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751873314693324, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314693514, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_F37C0F1BA2EC04B9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314693668, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314693769, "dur": 1937, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751873314695707, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314695894, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314696558, "dur": 1049, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314697608, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873314697801, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751873314698151, "dur": 127836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314825991, "dur": 2588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751873314828580, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314828655, "dur": 2550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751873314831205, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314831296, "dur": 2849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751873314834146, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314834225, "dur": 2908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751873314837134, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314837288, "dur": 258, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751873314837548, "dur": 9010, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751873314846559, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314846668, "dur": 5327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751873314851996, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314852097, "dur": 3984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751873314856081, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873314856173, "dur": 6872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751873314863069, "dur": 126073, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314626298, "dur": 14566, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314641008, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_0254B4EAD9FA5CB9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314641124, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_FDF1DF7383BE0CAD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314641281, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314641399, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_46E58BD6F1D896EF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314641520, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314641697, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_DB3537FAE77D4E90.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314641866, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314642025, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_97266B4C96017FCE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314642113, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314642198, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_B519677B2E750245.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314642313, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314642420, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_C268CA3985788B50.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314642547, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314642680, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2598F284265933B9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314642805, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314642912, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_12CDA413F762C33F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314643011, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314643118, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_06E5AC6350BD5612.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314643335, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314643539, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_7436A235A0073526.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314643745, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314644033, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_DBE3A542E9C167DB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314644171, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314644265, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_45806E81BE62D86B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314644384, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314644536, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5A2D3B4655B3286F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314644685, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314644776, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_0897B8A47BC70881.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314644951, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314645088, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314645234, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_32CA5EEFEC612C27.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314645516, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314645649, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_03EB462F708254BF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314645790, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314645920, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314645997, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314646169, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314646293, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314646394, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314646526, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314646663, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314646788, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314646912, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314647026, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314647130, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314647234, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314647335, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314647413, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314647527, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314647579, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314647744, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314647918, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314648073, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314648232, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314648368, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314648597, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751873314648670, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314648802, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314648911, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314649049, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314649253, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314649539, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314649634, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314649731, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314649900, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314650052, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314650218, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314650396, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314650548, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314650677, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314650836, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314650982, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314651263, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314651323, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314651418, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314652433, "dur": 597, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751873314653035, "dur": 1815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314654850, "dur": 1666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314656517, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314657740, "dur": 1533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314659273, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314660545, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314661656, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314662608, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314663516, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314664222, "dur": 914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314665137, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314666340, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314667267, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314668222, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314669078, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314669913, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314670585, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314671200, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314671869, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314672490, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314673167, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314673948, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314674618, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314675216, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314675879, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314676572, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314677419, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314678206, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314678981, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314679276, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314679965, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314680219, "dur": 71, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314680372, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314680632, "dur": 690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314681322, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314681628, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314681775, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314682095, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314682291, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314682682, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314682771, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314682822, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314683307, "dur": 2568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751873314685875, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314686061, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Editor.ref.dll_EA1A6D1CB62C8BFD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314686116, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314686229, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314686388, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314686460, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314687149, "dur": 1473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314688671, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314688746, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751873314689292, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314689394, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314689510, "dur": 4049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751873314693560, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314693809, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Editor.ref.dll_57B051C6BAF6DF9D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314693903, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314694008, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873314694361, "dur": 2853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751873314697215, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314697363, "dur": 128695, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314826127, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751873314826063, "dur": 3850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751873314830510, "dur": 980, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314984403, "dur": 438, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873314831787, "dur": 153072, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751873314988448, "dur": 605, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751873314989056, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314626305, "dur": 14592, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314640999, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_C4615A0A510AFA65.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873314641087, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314641195, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_7D5EE36C09E1C813.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873314641351, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314641461, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_5A158695308CA418.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873314641587, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314641763, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_2A0E14B20F8987CC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873314641928, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314642069, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_8192A56F15586443.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873314642176, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314642281, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_FD99BC8C7952C75B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873314642395, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314642584, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_1D0F8F342DF5CF0F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873314642703, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314642800, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_6DF747A371F03CBE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873314642929, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314643065, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_AEA8660476D6039F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873314643249, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314643436, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_B15AC01DD6F6A05B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873314643734, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314644047, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_A3EA01C4850945EE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873314644175, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314644338, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_875874FE279274C8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873314644491, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314644610, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D0B6A15278A42C4B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873314644763, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314644952, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314645095, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314645242, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_BA6BF233C73D6014.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873314645519, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314645681, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_6C262D80C93BD4F0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873314645812, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_665A96337EB7495A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873314646024, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314646188, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314646264, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314646423, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314646568, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314646709, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314646843, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314646971, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314647076, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314647179, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_4B16FAA5016376E3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873314647303, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314647396, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314647515, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314647576, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314647723, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314647913, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314648048, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314648203, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314648318, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314648566, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314648732, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314648835, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314648975, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314649141, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314649333, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314649614, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314649761, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314649912, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314650065, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314650189, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314650353, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314650485, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314650630, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314650755, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314650930, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314651218, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314651309, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314651397, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314651527, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314651619, "dur": 1905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314653524, "dur": 1682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314655206, "dur": 1697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314656903, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314657994, "dur": 1523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314659518, "dur": 1446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314660964, "dur": 914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314661878, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314662881, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314663745, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314664400, "dur": 973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314665373, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314666590, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314667489, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314668396, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314669208, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314669960, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314670611, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314671232, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314671867, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314672485, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314673154, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314673930, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314674608, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314675202, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314675863, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314676549, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314677390, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314678180, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314678937, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314679220, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314679952, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314680144, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314680369, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314680583, "dur": 692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314681277, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873314681829, "dur": 1009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751873314682839, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314683005, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314683070, "dur": 1235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751873314684306, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314684536, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873314684914, "dur": 7492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751873314692407, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314692586, "dur": 1540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751873314694127, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314694256, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PerformanceTesting.ref.dll_A9B74A479D61B912.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873314694338, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314694442, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873314694586, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751873314695085, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314695238, "dur": 1288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314696527, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873314696622, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751873314697213, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314697346, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.IK.Runtime.ref.dll_3971D385965D884C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873314697482, "dur": 128466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314825953, "dur": 3837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751873314829791, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314829851, "dur": 3745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751873314833606, "dur": 3351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751873314836959, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314837052, "dur": 4885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751873314841938, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314842251, "dur": 3125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751873314845377, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314845498, "dur": 2173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751873314847671, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314847833, "dur": 4374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751873314852208, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314852306, "dur": 2664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751873314854971, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314855070, "dur": 7620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751873314862775, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873314862838, "dur": 126269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314626311, "dur": 14596, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314641035, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314641176, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_A8B77A263A174862.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314641343, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314641442, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_3E4ECE7E8BB35882.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314641619, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_3D9BC3FD62E6039E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314641833, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314641924, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_79266F7FBDBF49B4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314642079, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314642173, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_110B7A9752F5AD38.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314642300, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314642373, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E00800116238B81E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314642620, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_AEBC23A8E4FA79A7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314642777, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314642876, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D7B1BF987A36C198.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314643014, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314643124, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A07FB6BEB1E7333E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314643354, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314643633, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_165FE63856186A10.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314643768, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314644060, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_A7772F47C16CAC56.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314644221, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314644357, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_60DE2D62F663D764.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314644514, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314644621, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8F6AF5EA65F36C03.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314644778, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314644885, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314645011, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_3FB038AB94CBD0EF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314645151, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314645240, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314645459, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_74A9378003EBEF66.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314645677, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314645742, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_B3E6C0B422B85093.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314645907, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314646087, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314646228, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_8C9D4507428A9FC0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314646371, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314646473, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314646618, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314646720, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314646893, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314647033, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314647155, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_11476E2D9783C34A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314647289, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314647387, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314647506, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314647558, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314647673, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314647838, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314648038, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314648187, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314648267, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314648368, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314648597, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314648733, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314648885, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314648977, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314649159, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314649387, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314649595, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314649706, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314649881, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314650038, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314650182, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314650346, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314650458, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314650609, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314650752, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314650872, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314650993, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751873314651096, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314651324, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314651428, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314651573, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314651647, "dur": 1932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314653579, "dur": 1713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314655292, "dur": 1638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314656931, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314658041, "dur": 1510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314659552, "dur": 1447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314661000, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314661918, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314662973, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314663813, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314664469, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314665491, "dur": 1250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314666741, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314667587, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314668495, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314669304, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314670045, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314670710, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314671323, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314671952, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314672574, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314673305, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314674043, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314674696, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314675307, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314675963, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314676659, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314677516, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314678302, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314679054, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314679763, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314680215, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314680375, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314680577, "dur": 693, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314681274, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314681534, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751873314682144, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314682226, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314682507, "dur": 1311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751873314683819, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314684106, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314684513, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314684690, "dur": 2164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751873314686854, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314686956, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Runtime.ref.dll_1DF7BBC55D029A75.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314687075, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314687513, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751873314688097, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314688199, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751873314688573, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314688653, "dur": 1586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314690261, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751873314690501, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314690619, "dur": 830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751873314691449, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314691546, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751873314692107, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314692277, "dur": 1385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751873314693663, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314693977, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751873314694069, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314694409, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751873314694857, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314695033, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314695088, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314695146, "dur": 1309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314696456, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314696775, "dur": 772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751873314697603, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873314697830, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751873314698445, "dur": 127537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314825990, "dur": 3257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751873314829247, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314829317, "dur": 3757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751873314833124, "dur": 7430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751873314840556, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314840757, "dur": 3828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751873314844586, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314844709, "dur": 5284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751873314849995, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314850075, "dur": 3680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751873314853756, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314853858, "dur": 2399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751873314856257, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314856334, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314856423, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314856749, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314856807, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314857155, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314857229, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314857385, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314857601, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314857673, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314857752, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314857829, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314857928, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314858029, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314858165, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314858339, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314858543, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314859234, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314859446, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314859743, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314859809, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314860068, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314860503, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314860952, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314861165, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751873314861244, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314861386, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751873314861471, "dur": 457, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314861931, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314862440, "dur": 125948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873314988471, "dur": 602, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751873314626318, "dur": 14709, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314641027, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_C5F3E4FEF96D893F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873314641200, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314641289, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_E7D994A1187DB9B1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873314641448, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314641553, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_013EC6C429305933.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873314641767, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314641885, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_CA2F35FCE4A30BF8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873314642076, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314642180, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_4475AF2AFFABA926.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873314642311, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314642415, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_89BDB88F64678315.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873314642542, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314642636, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_2D77F886ADC51F6A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873314642791, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314642885, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_CA0E1C7BC0C607E9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873314643018, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314643137, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A49295611C727860.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873314643351, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314643599, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_8D8F81C2AD007FF4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873314643731, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314644015, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_A6AABF0E6ADA0B25.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873314644175, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314644312, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_97D4D89498459919.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873314644455, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314644583, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_293131879DA5DD14.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873314644753, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314644849, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314644965, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_2CC8B0A3337F4D14.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873314645211, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314645426, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_ED2AB5385077AD72.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873314645631, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314645749, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_857F5E2A119BA0C9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873314645892, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314646021, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314646174, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314646265, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314646409, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314646549, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314646686, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314646817, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314646932, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314647084, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314647186, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_EC1F0C20321316E3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873314647315, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314647392, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314647546, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314647651, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314647887, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314648015, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314648141, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314648252, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314648464, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314648630, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314648743, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314648877, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314648997, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314649201, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314649422, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314649573, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314649667, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314649817, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314649997, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314650130, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314650300, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314650401, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314650589, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314650713, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314650867, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314650983, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314651276, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314651353, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314651474, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314651612, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314651697, "dur": 1885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314653582, "dur": 1778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314655360, "dur": 1627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314656987, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314658033, "dur": 1528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314659562, "dur": 1409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314660971, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314661883, "dur": 1008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314662891, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314663766, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314664420, "dur": 975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314665395, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314666635, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314667514, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314668441, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314669254, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314670001, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314670674, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314671308, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314671928, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314672549, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314673270, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314674019, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314674673, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314675270, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314675920, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314676614, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314677470, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314678250, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314678996, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314679434, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314680109, "dur": 92, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314680201, "dur": 88, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314680362, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314680576, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314680655, "dur": 594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314681250, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873314681497, "dur": 1265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751873314682762, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314682911, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314682977, "dur": 1441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751873314684419, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314684586, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEditorBridge.001.ref.dll_D33AAA747FCEED0A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873314684675, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEditorBridge.001.ref.dll_D33AAA747FCEED0A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873314684790, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873314685205, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314685276, "dur": 1287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751873314686564, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314686810, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873314686970, "dur": 5129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751873314692100, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314692245, "dur": 2916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1751873314695231, "dur": 401, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314823530, "dur": 1399, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314695637, "dur": 129302, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1751873314825947, "dur": 3758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751873314829705, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314829767, "dur": 3607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751873314833416, "dur": 2360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751873314835777, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314835857, "dur": 4117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751873314839975, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314840064, "dur": 3583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751873314843648, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314843799, "dur": 5353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751873314849153, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314849288, "dur": 3376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751873314852665, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873314852781, "dur": 2956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751873314855776, "dur": 6943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751873314862831, "dur": 126295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314626325, "dur": 14644, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314641017, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_5BD5A8CCC51B35E5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314641195, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314641325, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_F0C4CE25855A5940.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314641410, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314641520, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_9358C75F1190FC64.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314641763, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314641872, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FAD28CEFAD4EF7CD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314642096, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_355EDFCE7F3816F7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314642228, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314642310, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0D7C2BFD403ED278.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314642550, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314642669, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_5A04CF1DC9DD1390.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314642806, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314642908, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C30E9AD099929D47.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314643020, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314643150, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_DB4D8D23A5102F12.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314643361, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314643627, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_15FE89EB11B2B491.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314643766, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314644019, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_EE513D35A608BF93.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314644194, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314644324, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_4574CE91C336EF7D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314644462, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314644578, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_A79420259383E98A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314644755, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314644866, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314644987, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_C166689E8CA2A04E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314645170, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314645416, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_395D75F92AC91BB0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314645656, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314645786, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_02823EF6668D812C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314646026, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314646215, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314646305, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314646438, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314646619, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314646745, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314646869, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314646988, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314647104, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314647205, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_6717AAFEBC09DAE0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314647366, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314647513, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314647567, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314647704, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314647886, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314648032, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314648189, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314648278, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314648490, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314648652, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314648811, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314648932, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314649067, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314649269, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314649540, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314649639, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314649768, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314649955, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314650090, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314650277, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314650356, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314650507, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314650641, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314650786, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314650922, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314651142, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314651300, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314651370, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314651521, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314651601, "dur": 1925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314653527, "dur": 1723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314655252, "dur": 1658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314656911, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314658021, "dur": 1506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314659527, "dur": 1449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314660976, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314661899, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314662899, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314663770, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314664426, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314665404, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314666668, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314667548, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314668467, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314669277, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314670018, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314670680, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314671299, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314671933, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314672558, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314673273, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314674039, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314674682, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314675280, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314675950, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314676641, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314677507, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314678279, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314679015, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314679720, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314680361, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314680637, "dur": 605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314681242, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314681380, "dur": 3317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751873314684697, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314684890, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ref.dll_62EC740DD3AB3A8D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314685042, "dur": 2282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751873314687324, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314687473, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314687585, "dur": 1162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314688775, "dur": 822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314689649, "dur": 1460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751873314691109, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314691203, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314691265, "dur": 1821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314693087, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314693210, "dur": 2495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751873314695714, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314695928, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314696076, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751873314696330, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314696453, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314696571, "dur": 824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751873314697395, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314697507, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314697755, "dur": 829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751873314698606, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314698708, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751873314698953, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873314699093, "dur": 126863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314825960, "dur": 5621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751873314831582, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314831718, "dur": 4808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751873314836527, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314836619, "dur": 3946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751873314840566, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314840664, "dur": 6680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751873314847346, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314847497, "dur": 3158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751873314850656, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314850740, "dur": 4467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751873314855207, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314855287, "dur": 7002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751873314862342, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314862415, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873314863067, "dur": 126072, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751873314991287, "dur": 1003, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1751873313577192, "dur": 823506, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751873313578030, "dur": 117473, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751873314284721, "dur": 4979, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751873314289705, "dur": 110984, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751873314293304, "dur": 66327, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751873314406184, "dur": 1030, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751873314405747, "dur": 1645, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751873313067565, "dur": 2381, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751873313069951, "dur": 15710, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751873313085782, "dur": 99, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751873313086231, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_91118A2F728DAD9D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751873313086928, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_C59F701EDC7B3906.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751873313091307, "dur": 546, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751873313093610, "dur": 299, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751873313111578, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751873313112138, "dur": 120, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751873313112341, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751873313121948, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Aseprite.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751873313085887, "dur": 39354, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751873313125249, "dur": 82575, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751873313207892, "dur": 81931, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751873313289873, "dur": 136738, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751873313426680, "dur": 503, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751873313085838, "dur": 39418, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313125289, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751873313125569, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_9FEDC77DF18D321C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873313125925, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_5BD5A8CCC51B35E5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873313126107, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_E7D994A1187DB9B1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873313126382, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_479F21D64DDF5A5C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873313126479, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_2A0E14B20F8987CC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873313126674, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313126766, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0D7C2BFD403ED278.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873313126939, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313126993, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_845B1BE9B09512AE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873313127096, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313127193, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_070DA0E44072E4AB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873313127314, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313127459, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A49295611C727860.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873313127574, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313127695, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_15FE89EB11B2B491.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873313127815, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313128047, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_A3EA01C4850945EE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873313128147, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313128276, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_875874FE279274C8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873313128401, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D0B6A15278A42C4B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873313128518, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751873313128857, "dur": 506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751873313129370, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751873313129951, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751873313130098, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751873313130310, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751873313130498, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751873313130883, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751873313131254, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751873313131358, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751873313131734, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313131862, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751873313132385, "dur": 11392, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751873313143779, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313143938, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313144019, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313144211, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313144439, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751873313144848, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751873313145261, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313145412, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313145679, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313145924, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751873313146383, "dur": 736, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313147121, "dur": 1410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313148532, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313149380, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313150161, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313150998, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313151746, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313152510, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313153220, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313154001, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313154682, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873313155186, "dur": 1305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751873313156491, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313156639, "dur": 5659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751873313162300, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313162392, "dur": 4570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751873313166962, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313167040, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873313167541, "dur": 763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873313168317, "dur": 2923, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751873313171240, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313171478, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873313171963, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873313172536, "dur": 2066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751873313174643, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873313175132, "dur": 3692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751873313178824, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313179002, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313179732, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313180312, "dur": 396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313180708, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313181288, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313182039, "dur": 1386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313183426, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313183918, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313184196, "dur": 92, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313184658, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313184796, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873313184890, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751873313185197, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313185410, "dur": 656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751873313186067, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751873313186156, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751873313186474, "dur": 21370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313085841, "dur": 39450, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313125294, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751873313125552, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_E6099293577F65EB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313125878, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313125960, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_8B7F52F4EF803EEA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313126186, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313126244, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_3E4ECE7E8BB35882.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313126355, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_9358C75F1190FC64.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313126433, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313126491, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_91118A2F728DAD9D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313126634, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C9234425C371F41A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313126686, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313126828, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_061233166775A3D8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313126923, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313126978, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_2D77F886ADC51F6A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313127107, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313127228, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C30E9AD099929D47.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313127308, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313127449, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A07FB6BEB1E7333E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313127523, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313127648, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_8D8F81C2AD007FF4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313127776, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313128013, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_DBE3A542E9C167DB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313128126, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313128240, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_4574CE91C336EF7D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313128319, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313128378, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_A8529918C73873A7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313128546, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751873313128955, "dur": 1460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751873313130415, "dur": 526, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313130955, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313131139, "dur": 1842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751873313132981, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313133177, "dur": 10647, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751873313143867, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313144017, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313144204, "dur": 1012, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313145217, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313145387, "dur": 8510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751873313153898, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313154071, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_BD0E97F52A0DF729.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313154179, "dur": 809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313155003, "dur": 2227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751873313157231, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313157330, "dur": 898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313158256, "dur": 2954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751873313161211, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313161456, "dur": 1194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313162674, "dur": 3351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751873313166025, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313166174, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Aseprite.Common.ref.dll_3F93312DF16ABE8A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313166232, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313166319, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313166417, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313166510, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.PixelPerfect.ref.dll_8E9298DC6B12ED57.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313166613, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313167103, "dur": 759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313167883, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313168151, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313168289, "dur": 2204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751873313170493, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313170639, "dur": 2451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751873313173090, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313173314, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313173898, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313174439, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313174666, "dur": 1930, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751873313176596, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313176781, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313177403, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313178064, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313178699, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313179412, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313180036, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313180613, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313181188, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313181896, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313182920, "dur": 1085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313184033, "dur": 571, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.ugui@a0f5d16b3c82/Runtime/TMP/TMP_ListPool.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751873313184005, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313184632, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313184682, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313184814, "dur": 533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751873313185348, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313185551, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751873313186047, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751873313186131, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751873313186492, "dur": 21324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313085845, "dur": 39462, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313125309, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_A93D6DF8E051997E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873313125840, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_A93D6DF8E051997E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873313125930, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_FDF1DF7383BE0CAD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873313126088, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313126192, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_46E58BD6F1D896EF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873313126371, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_013EC6C429305933.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873313126591, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_97266B4C96017FCE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873313126679, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_110B7A9752F5AD38.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873313126863, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313126924, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_C268CA3985788B50.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873313127054, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_5A04CF1DC9DD1390.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873313127221, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313127279, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_12CDA413F762C33F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873313127520, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_36001A2C42EABFDD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873313127691, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313127769, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_165FE63856186A10.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873313128016, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313128072, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_A7772F47C16CAC56.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873313128231, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313128316, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_60DE2D62F663D764.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873313128433, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8F6AF5EA65F36C03.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873313128685, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751873313128808, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751873313129156, "dur": 863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751873313130025, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751873313130161, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313130318, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751873313130830, "dur": 663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751873313131557, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313131709, "dur": 1076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751873313132791, "dur": 10900, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751873313143734, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313143900, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313144036, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313144239, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313144472, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751873313144689, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751873313144831, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313144901, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751873313144993, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313145211, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751873313145269, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313145398, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313145636, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751873313146103, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313146188, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751873313146255, "dur": 501, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313146759, "dur": 555, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Runtime.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751873313146759, "dur": 1745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313148504, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313149384, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313150159, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313150887, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313151644, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313152418, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313153139, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313153869, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313154594, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873313154976, "dur": 3115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751873313158091, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313158202, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873313158524, "dur": 4310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751873313162836, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313162960, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751873313163399, "dur": 3487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751873313166886, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313167197, "dur": 2928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1751873313170176, "dur": 836, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313286098, "dur": 1201, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751873313171283, "dur": 116072, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1751873313085852, "dur": 39460, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313125314, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_5651B02FF685D981.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873313125882, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_C4615A0A510AFA65.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873313125963, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313126081, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_A8B77A263A174862.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873313126208, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313126297, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_5A158695308CA418.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873313126418, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_3D9BC3FD62E6039E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873313126539, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313126602, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_8192A56F15586443.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873313126658, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313126730, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_B519677B2E750245.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873313126880, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313126938, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_1D0F8F342DF5CF0F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873313127010, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313127089, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_6DF747A371F03CBE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873313127246, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313127303, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_34F8FBC3386DE99E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873313127501, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_DB4D8D23A5102F12.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873313127730, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313127793, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_65DFED6065744ECD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873313128058, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313128145, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_BA115901BA18BB2D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873313128300, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313128361, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_06C0A05E69B9BD23.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873313128474, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_EB44D0B764B7C8DC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873313128610, "dur": 743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751873313129358, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751873313130113, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313130274, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873313130337, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751873313130874, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751873313131026, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751873313131475, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751873313131626, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313131714, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751873313131866, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751873313131994, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751873313132374, "dur": 11171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751873313143747, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313143886, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313144038, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313144267, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751873313144969, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313145090, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313145306, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313145448, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313145798, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313145998, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751873313146105, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313146271, "dur": 517, "ph": "X", "name": "File", "args": {"detail": "Assets/_Assets/MUP/UI/UIManager.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751873313146788, "dur": 562, "ph": "X", "name": "File", "args": {"detail": "Assets/_Assets/MUP/Feel/FeelManager.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751873313146196, "dur": 2217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313148413, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313149315, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313150090, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313150938, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313151689, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313152465, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313153173, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313153921, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313154646, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873313154892, "dur": 11655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751873313166564, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313166703, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313166815, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873313167155, "dur": 6882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751873313174037, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313174147, "dur": 752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873313174914, "dur": 4114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751873313179028, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751873313179235, "dur": 1790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873313181055, "dur": 3880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751873313184967, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873313185031, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751873313185336, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873313185418, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751873313185762, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751873313185836, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751873313186272, "dur": 21560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313085861, "dur": 39464, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313125338, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_C5C1DFA646678D5B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873313125927, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_C5F3E4FEF96D893F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873313126101, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313126168, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_FBD6848B40F9D84E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873313126372, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313126428, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_DB3537FAE77D4E90.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873313126528, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313126586, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_79266F7FBDBF49B4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873313126690, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_4475AF2AFFABA926.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873313126934, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_58154A82C8AEEED2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873313127005, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313127082, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2598F284265933B9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873313127265, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313127377, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_AEA8660476D6039F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873313127475, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313127557, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_462079EBF000A463.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873313127714, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313127783, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_ED9F64302AB7CA36.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873313128097, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_C1F8E474D4FC94AE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873313128326, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6799CBAE2582DA9C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873313128453, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_0897B8A47BC70881.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873313128603, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751873313128948, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873313129031, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751873313129723, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751873313130118, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313130232, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313130293, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751873313130525, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751873313130854, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751873313131321, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751873313131522, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751873313131734, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751873313132147, "dur": 765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751873313132916, "dur": 10878, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751873313143796, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313143930, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313144081, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313144245, "dur": 734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751873313144980, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313145183, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313145248, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313145347, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313145500, "dur": 368, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313145875, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313146029, "dur": 585, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313146620, "dur": 589, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751873313146620, "dur": 1851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313148471, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313149374, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313150152, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313150993, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313151744, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313152512, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313153216, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313153958, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313154662, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873313154914, "dur": 2163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751873313157077, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313157216, "dur": 934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873313158186, "dur": 2684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751873313160871, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313160985, "dur": 2974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751873313163959, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313164045, "dur": 1991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873313166074, "dur": 2362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751873313168436, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313168528, "dur": 664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873313169214, "dur": 2157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751873313171371, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313171534, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873313171981, "dur": 1586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751873313173567, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313173667, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313174213, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873313174736, "dur": 1418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751873313176154, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313176258, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll_8B099D8C97AF393F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751873313176364, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313177009, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313177646, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313178290, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313178828, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313179587, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313180184, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313180669, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313181262, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313182015, "dur": 1279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313183294, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313184030, "dur": 576, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.ugui@a0f5d16b3c82/Runtime/TMP/TMP_SpriteAsset.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751873313183953, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313184696, "dur": 114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313184811, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313185417, "dur": 22272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751873313207690, "dur": 112, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313085868, "dur": 39483, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313125353, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A137065F34CC9C45.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873313125917, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_0254B4EAD9FA5CB9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873313126112, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313126193, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_E5822FB88B5F8525.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873313126425, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313126523, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_CA2F35FCE4A30BF8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873313126618, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_355EDFCE7F3816F7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873313126670, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313126744, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_FD99BC8C7952C75B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873313126891, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313126950, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_6C9DA28BBAE3FB82.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873313127013, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313127101, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_0DC0DA8529CF1082.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873313127267, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313127382, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0462768E64EABD0A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873313127475, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313127612, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_FF3C62C209E1F2AF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873313127757, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313127985, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_EE513D35A608BF93.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873313128076, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313128173, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_8D21704F39F1057D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873313128314, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313128388, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_A79420259383E98A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873313128496, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751873313128707, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751873313128829, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751873313129290, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751873313129398, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751873313129999, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751873313130244, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751873313130505, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751873313130831, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751873313131105, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751873313131440, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751873313131558, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751873313131785, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751873313131970, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751873313132320, "dur": 11195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PsdPlugin.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751873313143647, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751873313143736, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313143797, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313143980, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313144160, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313144394, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751873313144799, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313144875, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751873313145277, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313145457, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313145828, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313146012, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751873313146145, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313146366, "dur": 750, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751873313146366, "dur": 2107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313148473, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313149370, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313150141, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313150977, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313151720, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313152580, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313153306, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313154087, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313154727, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873313155094, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313155175, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873313155425, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873313155727, "dur": 1458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873313157212, "dur": 1952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751873313159165, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313159252, "dur": 997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873313160249, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313160306, "dur": 2850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751873313163157, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313163240, "dur": 731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873313163999, "dur": 3860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751873313167859, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313168017, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873313168171, "dur": 1938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751873313170110, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313170345, "dur": 2210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751873313172555, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313172658, "dur": 2119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751873313174828, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313175081, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313175492, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313175785, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313176173, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313176811, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313177421, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313178086, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313178731, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313179442, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313180067, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313180634, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313181205, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313181957, "dur": 1064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313183021, "dur": 1027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313184711, "dur": 62, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313184822, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751873313185409, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873313185528, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751873313185632, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751873313185990, "dur": 21844, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313085875, "dur": 39489, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313125380, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_661E9919313836F1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873313125905, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_9EFE93A4B6B28B73.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873313126096, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_7D5EE36C09E1C813.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873313126421, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_54E2C669D74DB25A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873313126549, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_39B25D32CFFE745B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873313126672, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313126787, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E00800116238B81E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873313126899, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313126969, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_AEBC23A8E4FA79A7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873313127039, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313127127, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_CA0E1C7BC0C607E9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873313127271, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313127411, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_06E5AC6350BD5612.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873313127490, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313127627, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_7436A235A0073526.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873313127746, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313127976, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_A6AABF0E6ADA0B25.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873313128090, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313128205, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_97D4D89498459919.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873313128395, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_293131879DA5DD14.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873313128509, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751873313128792, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751873313128930, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751873313129187, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751873313129720, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751873313130010, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751873313130308, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751873313130826, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751873313131455, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751873313131541, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313131602, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751873313131781, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751873313131951, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751873313132183, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751873313132767, "dur": 10908, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751873313143689, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751873313143754, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313143948, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313144099, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313144307, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751873313144856, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313144949, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751873313145204, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313145382, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313145589, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313145935, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/ScriptablePacker.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751873313146188, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313146356, "dur": 727, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/WindowsBase.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751873313146356, "dur": 2139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313148495, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313149368, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313150139, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313150980, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313151731, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313152485, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313153198, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313153949, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313154655, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873313154962, "dur": 1469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751873313156431, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313156572, "dur": 1143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873313157743, "dur": 801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873313158564, "dur": 1280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873313159861, "dur": 3392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751873313163254, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313163384, "dur": 787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873313164194, "dur": 3769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751873313168059, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873313168198, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873313168377, "dur": 2585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751873313170963, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313171065, "dur": 2784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751873313173849, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313173984, "dur": 686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873313174690, "dur": 2772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751873313177462, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313177572, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313178242, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313178896, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313179636, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313180235, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313180715, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313181299, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313182063, "dur": 1350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313183413, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313183837, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313184023, "dur": 77, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313184689, "dur": 119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313184808, "dur": 607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313185416, "dur": 1419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751873313186835, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751873313186913, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751873313187150, "dur": 20686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313085883, "dur": 39491, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313125375, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5CDB3D0CA3CB09B8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873313125923, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_D7331C31AE624D95.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873313126089, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313126157, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_F0C4CE25855A5940.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873313126431, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313126496, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FAD28CEFAD4EF7CD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873313126800, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313126888, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_89BDB88F64678315.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873313127022, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313127116, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D7B1BF987A36C198.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873313127261, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313127358, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_9179AE1968B95645.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873313127473, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313127576, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_B15AC01DD6F6A05B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873313127737, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313127841, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_C59F701EDC7B3906.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873313128061, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313128133, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_45806E81BE62D86B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873313128287, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313128347, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5A2D3B4655B3286F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873313128439, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_4782BF78D66FB2E5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873313128689, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751873313129273, "dur": 829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751873313130247, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751873313130603, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751873313130868, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751873313131277, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751873313131410, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751873313131735, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751873313132262, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751873313132868, "dur": 10893, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751873313143764, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313143914, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313144067, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313144274, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313144454, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751873313144664, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751873313145236, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313145350, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313145539, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313145892, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751873313146182, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313146284, "dur": 537, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.test-framework.performance@92d1d09a72ed/Editor/TestResultsParser.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751873313146821, "dur": 540, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.test-framework.performance@92d1d09a72ed/Editor/TestReportGraph/TestReportWindow.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751873313146284, "dur": 2161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313148446, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313149357, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313150133, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313150988, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313151741, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313152499, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313153209, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313153995, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313154670, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873313155192, "dur": 2737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751873313157930, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313158030, "dur": 3315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751873313161347, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313161513, "dur": 2565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751873313164078, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313164254, "dur": 767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873313165045, "dur": 3393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751873313168438, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313168513, "dur": 1625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751873313170138, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313170202, "dur": 2810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751873313173012, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313173344, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313173935, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313174489, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873313174927, "dur": 1354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751873313176281, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313176428, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313177062, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313177694, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313178326, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313178674, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313179368, "dur": 1773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873313181170, "dur": 3534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751873313184704, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751873313184789, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873313184973, "dur": 1310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751873313186307, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873313186386, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751873313186833, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873313186911, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751873313187162, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751873313187234, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751873313188280, "dur": 237661, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751873313428508, "dur": 572, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 75155, "tid": 1786, "ts": 1751873315017060, "dur": 3704, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 75155, "tid": 1786, "ts": 1751873315023170, "dur": 28, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 75155, "tid": 1786, "ts": 1751873315023343, "dur": 12, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 75155, "tid": 1786, "ts": 1751873315020951, "dur": 2214, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 75155, "tid": 1786, "ts": 1751873315023271, "dur": 71, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 75155, "tid": 1786, "ts": 1751873315023372, "dur": 391, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 75155, "tid": 1786, "ts": 1751873315009702, "dur": 14776, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}