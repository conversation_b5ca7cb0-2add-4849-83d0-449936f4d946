# Enhanced UIManager Documentation

## Overview

The enhanced UIManager provides a comprehensive UI management system for Unity projects with the following key features:

- **View Lifecycle Management**: Automatic initialization, showing, hiding, and cleanup of UI views
- **Navigation Stack**: Scene-based navigation with history support
- **Popup Management**: Overlay popup system with stacking support
- **Transition Animations**: Smooth fade-in/fade-out transitions between views
- **Memory Management**: View pooling and caching to optimize performance
- **Event System**: Custom events for UI interactions and state changes
- **Error Handling**: Robust error handling with detailed logging

## Architecture

### Core Components

1. **View Base Class**: Abstract base class that all UI views must inherit from
2. **UIManager Singleton**: Central manager for all UI operations
3. **Canvas System**: Separate canvases for scenes and popups
4. **Transition System**: Animation system for smooth view transitions

### View Lifecycle

Each view goes through the following lifecycle:
1. **Init()**: Initialize the view (called once)
2. **Enter()**: Prepare the view for display
3. **Show()**: Make the view visible
4. **Hide()**: Hide the view
5. **Exit()**: Clean up the view

## Setup Instructions

### 1. Canvas Configuration

Create two Canvas objects in your scene:
- **Scene Canvas**: For full-screen views (sorting order: 0)
- **Popup Canvas**: For overlay popups (sorting order: 1+)

Assign these canvases to the UIManager in the inspector.

### 2. Creating Views

```csharp
using MUP.UI;
using UnityEngine;

public class MainMenuView : View
{
    [SerializeField] private Button playButton;
    [SerializeField] private Button settingsButton;

    public override void Init()
    {
        // Initialize UI components
        playButton.onClick.AddListener(OnPlayClicked);
        settingsButton.onClick.AddListener(OnSettingsClicked);
    }

    public override void Enter()
    {
        // Prepare view for display
        // Reset animations, update data, etc.
    }

    public override void Show()
    {
        // View is now visible
        // Start any entrance animations here
    }

    public override void Hide()
    {
        // View is being hidden
        // Stop animations, save state, etc.
    }

    public override void Exit()
    {
        // Clean up resources
        // Remove listeners, stop coroutines, etc.
        playButton.onClick.RemoveListener(OnPlayClicked);
        settingsButton.onClick.RemoveListener(OnSettingsClicked);
    }

    private void OnPlayClicked()
    {
        UIManager.Instance.ShowScene("GameplayView");
    }

    private void OnSettingsClicked()
    {
        UIManager.Instance.ShowPopup("SettingsPopup");
    }
}
```

### 3. Registering Views

```csharp
public class GameInitializer : MonoBehaviour
{
    [SerializeField] private GameObject mainMenuPrefab;
    [SerializeField] private GameObject gameplayPrefab;
    [SerializeField] private GameObject settingsPopupPrefab;

    private void Start()
    {
        // Initialize UIManager
        UIManager.Instance.Init();

        // Register view prefabs
        UIManager.Instance.RegisterViewPrefab("MainMenuView", mainMenuPrefab);
        UIManager.Instance.RegisterViewPrefab("GameplayView", gameplayPrefab);
        UIManager.Instance.RegisterViewPrefab("SettingsPopup", settingsPopupPrefab);

        // Show initial view
        UIManager.Instance.ShowScene("MainMenuView");
    }
}
```

## API Reference

### Scene Management

```csharp
// Show a scene view (replaces current scene)
UIManager.Instance.ShowScene("ViewId", useTransition: true, transitionDuration: 0.3f);

// Hide current scene
UIManager.Instance.HideCurrentScene(useTransition: true);

// Navigate back to previous scene
bool success = UIManager.Instance.NavigateBack();

// Check if back navigation is possible
bool canGoBack = UIManager.Instance.CanNavigateBack();

// Clear navigation history
UIManager.Instance.ClearSceneHistory();
```

### Popup Management

```csharp
// Show a popup
UIManager.Instance.ShowPopup("PopupId", useTransition: true);

// Hide specific popup
UIManager.Instance.HidePopup("PopupId", useTransition: true);

// Hide topmost popup
UIManager.Instance.HideTopPopup(useTransition: true);

// Hide all popups
UIManager.Instance.HideAllPopups(useTransition: true);
```

### View Registration

```csharp
// Register a prefab for later instantiation
UIManager.Instance.RegisterViewPrefab("ViewId", prefabGameObject);

// Register an existing view instance
UIManager.Instance.RegisterView("ViewId", viewInstance);
```

### View Access

```csharp
// Get current scene
View currentScene = UIManager.Instance.GetCurrentScene();

// Get current popup
View currentPopup = UIManager.Instance.GetCurrentPopup();

// Get specific view
View view = UIManager.Instance.GetView("ViewId");

// Check if view is visible
bool isVisible = UIManager.Instance.IsViewVisible("ViewId");

// Get navigation history
View[] sceneHistory = UIManager.Instance.GetSceneHistory();
View[] popupStack = UIManager.Instance.GetPopupStack();
```

### Event System

```csharp
// Subscribe to view events
UIManager.Instance.SubscribeToViewEvent("OnShow_MainMenuView", OnMainMenuShown);

// Unsubscribe from view events
UIManager.Instance.UnsubscribeFromViewEvent("OnShow_MainMenuView", OnMainMenuShown);

// Subscribe to global UI events
UIManager.OnSceneShown += OnAnySceneShown;
UIManager.OnPopupShown += OnAnyPopupShown;
UIManager.OnNavigationChanged += OnNavigationChanged;

private void OnMainMenuShown(View view)
{
    Debug.Log($"Main menu shown: {view.name}");
}

private void OnNavigationChanged(View fromView, View toView)
{
    Debug.Log($"Navigation: {fromView?.name} -> {toView?.name}");
}
```

## Configuration Options

### Animation Settings

- **Default Transition Duration**: Default time for view transitions (0.3s)
- **Ease In Curve**: Animation curve for showing views
- **Ease Out Curve**: Animation curve for hiding views

### Memory Management

- **Max Cached Views**: Maximum number of views to keep in cache (10)
- **Enable View Pooling**: Whether to cache view instances for reuse

## Best Practices

### 1. View Design
- Keep views focused on a single responsibility
- Use the lifecycle methods appropriately
- Clean up resources in Exit() method
- Avoid heavy operations in Show()/Hide()

### 2. Navigation
- Use meaningful view IDs
- Plan your navigation flow carefully
- Consider using navigation back for user experience
- Clear history when appropriate (e.g., after login)

### 3. Performance
- Enable view pooling for frequently used views
- Limit the number of cached views
- Use transitions judiciously (disable for performance-critical moments)
- Preload important views during loading screens

### 4. Error Handling
- Always check return values from navigation methods
- Handle cases where views might not exist
- Use try-catch blocks in view lifecycle methods
- Monitor the console for UIManager warnings/errors

## Troubleshooting

### Common Issues

1. **View not showing**: Check if view is registered and prefab has View component
2. **Transitions not working**: Ensure CanvasGroup component is available
3. **Memory leaks**: Make sure to clean up in Exit() method
4. **Navigation issues**: Verify canvas assignments and sorting orders

### Debug Information

The UIManager provides detailed logging for:
- View registration and instantiation
- Transition states and errors
- Navigation changes
- Memory management operations

Enable Unity console to see these logs during development.
