using MUP.Core;
using UnityEngine;
using System.Collections.Generic;

namespace MUP.UI
{
    public class UIManager : MUP<PERSON>ingleton<UIManager>
    {
        [SerializeField] private Canvas sceneCanvas;
        [SerializeField] private Canvas popupCanvas;

        private Stack<View> sceneStack = new Stack<View>();
        private Stack<View> popupStack = new Stack<View>();

        public void Init()
        {

        }

        public void ShowScene(string viewId)
        {

        }

        public void HideScene(string viewId)
        {

        }

        public void ShowPopup(string viewId)
        {

        }


        public void HidePopup(string viewId)
        {

        }

        public View GetCurrentScene()
        {
            return sceneStack.Count > 0 ? sceneStack.Peek() : null;
        }

        public View GetCurrentPopup()
        {
            return popupStack.Count > 0 ? popupStack.Peek() : null;
        }
    }
}
